using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using ProManage.Modules.Models;
using ProManage.Modules.Connections;

namespace ProManage.Forms.Dialogs
{
    /// <summary>
    /// Dialog for creating and editing roles
    /// </summary>
    public partial class RoleCreateEditDialog : XtraForm
    {
        #region Private Fields

        private Role _role;
        private bool _isEditMode;

        #endregion

        #region Properties

        /// <summary>
        /// Gets the role data from the dialog
        /// </summary>
        public Role Role => _role;

        #endregion

        #region Constructors

        /// <summary>
        /// Constructor for creating a new role
        /// </summary>
        public RoleCreateEditDialog()
        {
            InitializeComponent();
            _isEditMode = false;
            _role = new Role();
            InitializeDialog();
        }

        /// <summary>
        /// Constructor for editing an existing role
        /// </summary>
        /// <param name="role">Role to edit</param>
        public RoleCreateEditDialog(Role role)
        {
            InitializeComponent();
            _isEditMode = true;
            _role = role?.Clone() ?? new Role();
            InitializeDialog();
            LoadRoleData();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initialize dialog settings
        /// </summary>
        private void InitializeDialog()
        {
            this.Text = _isEditMode ? "Edit Role" : "Create New Role";
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            
            // Set default values for new role
            if (!_isEditMode)
            {
                chkIsActive.Checked = true;
                radioNoPermissions.Checked = true;
            }
            
            LoadRolesForCopy();
        }

        /// <summary>
        /// Load existing role data into controls
        /// </summary>
        private void LoadRoleData()
        {
            if (_role == null) return;

            txtRoleName.Text = _role.RoleName;
            txtDescription.Text = _role.Description;
            chkIsActive.Checked = _role.IsActive;
            
            // Disable copy options for edit mode
            radioNoPermissions.Checked = true;
            radioCopyFromRole.Enabled = false;
            cmbCopyFromRole.Enabled = false;
        }

        /// <summary>
        /// Load roles for copy dropdown
        /// </summary>
        private void LoadRolesForCopy()
        {
            try
            {
                var roles = PermissionDatabaseService.GetAllRoles();
                cmbCopyFromRole.Properties.Items.Clear();
                
                foreach (var role in roles)
                {
                    if (!_isEditMode || role.RoleId != _role.RoleId)
                    {
                        cmbCopyFromRole.Properties.Items.Add(new { Text = role.RoleName, Value = role.RoleId });
                    }
                }
                
                cmbCopyFromRole.Properties.DisplayMember = "Text";
                cmbCopyFromRole.Properties.ValueMember = "Value";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading roles: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle radio button changes
        /// </summary>
        private void RadioButton_CheckedChanged(object sender, EventArgs e)
        {
            cmbCopyFromRole.Enabled = radioCopyFromRole.Checked;
        }

        /// <summary>
        /// Handle Save button click
        /// </summary>
        private void btnSave_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                SaveRoleData();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        /// <summary>
        /// Handle Cancel button click
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion

        #region Validation and Save

        /// <summary>
        /// Validate user input
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        private bool ValidateInput()
        {
            // Check role name
            if (string.IsNullOrWhiteSpace(txtRoleName.Text))
            {
                MessageBox.Show("Role name is required.", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRoleName.Focus();
                return false;
            }

            // Check role name uniqueness
            if (!PermissionDatabaseService.IsRoleNameUnique(txtRoleName.Text.Trim(), _role.RoleId))
            {
                MessageBox.Show("Role name already exists. Please choose a different name.", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRoleName.Focus();
                return false;
            }

            // Check copy from role selection if needed
            if (radioCopyFromRole.Checked && cmbCopyFromRole.SelectedItem == null)
            {
                MessageBox.Show("Please select a role to copy permissions from.", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbCopyFromRole.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// Save role data to the role object
        /// </summary>
        private void SaveRoleData()
        {
            _role.RoleName = txtRoleName.Text.Trim();
            _role.Description = txtDescription.Text.Trim();
            _role.IsActive = chkIsActive.Checked;
            
            if (!_isEditMode)
            {
                _role.CreatedDate = DateTime.Now;
            }
            _role.ModifiedDate = DateTime.Now;
        }

        /// <summary>
        /// Get the selected role ID to copy permissions from
        /// </summary>
        /// <returns>Role ID or 0 if no copy selected</returns>
        public int GetCopyFromRoleId()
        {
            if (radioCopyFromRole.Checked && cmbCopyFromRole.SelectedItem != null)
            {
                dynamic selectedItem = cmbCopyFromRole.SelectedItem;
                return (int)selectedItem.Value;
            }
            return 0;
        }

        #endregion
    }
}
