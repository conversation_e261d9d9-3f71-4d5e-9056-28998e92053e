namespace ProManage.Forms.Dialogs
{
    partial class RoleCreateEditDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtRoleName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.txtDescription = new DevExpress.XtraEditors.MemoEdit();
            this.chkIsActive = new DevExpress.XtraEditors.CheckEdit();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.cmbCopyFromRole = new DevExpress.XtraEditors.ComboBoxEdit();
            this.radioCopyFromRole = new System.Windows.Forms.RadioButton();
            this.radioNoPermissions = new System.Windows.Forms.RadioButton();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.btnCancel = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.txtRoleName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDescription.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsActive.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbCopyFromRole.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(18, 18);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(58, 13);
            this.labelControl1.TabIndex = 0;
            this.labelControl1.Text = "Role Name:";
            // 
            // txtRoleName
            // 
            this.txtRoleName.Location = new System.Drawing.Point(18, 37);
            this.txtRoleName.Name = "txtRoleName";
            this.txtRoleName.Size = new System.Drawing.Size(350, 20);
            this.txtRoleName.TabIndex = 1;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(18, 73);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(57, 13);
            this.labelControl2.TabIndex = 2;
            this.labelControl2.Text = "Description:";
            // 
            // txtDescription
            // 
            this.txtDescription.Location = new System.Drawing.Point(18, 92);
            this.txtDescription.Name = "txtDescription";
            this.txtDescription.Size = new System.Drawing.Size(350, 80);
            this.txtDescription.TabIndex = 3;
            // 
            // chkIsActive
            // 
            this.chkIsActive.Location = new System.Drawing.Point(18, 188);
            this.chkIsActive.Name = "chkIsActive";
            this.chkIsActive.Properties.Caption = "Active Role";
            this.chkIsActive.Size = new System.Drawing.Size(75, 19);
            this.chkIsActive.TabIndex = 4;
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.cmbCopyFromRole);
            this.groupControl1.Controls.Add(this.radioCopyFromRole);
            this.groupControl1.Controls.Add(this.radioNoPermissions);
            this.groupControl1.Location = new System.Drawing.Point(18, 223);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(350, 100);
            this.groupControl1.TabIndex = 5;
            this.groupControl1.Text = "Initial Permissions";
            // 
            // radioNoPermissions
            // 
            this.radioNoPermissions.AutoSize = true;
            this.radioNoPermissions.Location = new System.Drawing.Point(15, 35);
            this.radioNoPermissions.Name = "radioNoPermissions";
            this.radioNoPermissions.Size = new System.Drawing.Size(155, 17);
            this.radioNoPermissions.TabIndex = 0;
            this.radioNoPermissions.TabStop = true;
            this.radioNoPermissions.Text = "No permissions (recommended)";
            this.radioNoPermissions.UseVisualStyleBackColor = true;
            this.radioNoPermissions.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            // 
            // radioCopyFromRole
            // 
            this.radioCopyFromRole.AutoSize = true;
            this.radioCopyFromRole.Location = new System.Drawing.Point(15, 58);
            this.radioCopyFromRole.Name = "radioCopyFromRole";
            this.radioCopyFromRole.Size = new System.Drawing.Size(75, 17);
            this.radioCopyFromRole.TabIndex = 1;
            this.radioCopyFromRole.TabStop = true;
            this.radioCopyFromRole.Text = "Copy from:";
            this.radioCopyFromRole.UseVisualStyleBackColor = true;
            this.radioCopyFromRole.CheckedChanged += new System.EventHandler(this.RadioButton_CheckedChanged);
            // 
            // cmbCopyFromRole
            // 
            this.cmbCopyFromRole.Enabled = false;
            this.cmbCopyFromRole.Location = new System.Drawing.Point(96, 57);
            this.cmbCopyFromRole.Name = "cmbCopyFromRole";
            this.cmbCopyFromRole.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cmbCopyFromRole.Size = new System.Drawing.Size(240, 20);
            this.cmbCopyFromRole.TabIndex = 2;
            // 
            // btnSave
            // 
            this.btnSave.Location = new System.Drawing.Point(212, 339);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(75, 23);
            this.btnSave.TabIndex = 6;
            this.btnSave.Text = "Save";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Location = new System.Drawing.Point(293, 339);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(75, 23);
            this.btnCancel.TabIndex = 7;
            this.btnCancel.Text = "Cancel";
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // RoleCreateEditDialog
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(386, 384);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.chkIsActive);
            this.Controls.Add(this.txtDescription);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.txtRoleName);
            this.Controls.Add(this.labelControl1);
            this.Name = "RoleCreateEditDialog";
            this.Text = "Role Management";
            ((System.ComponentModel.ISupportInitialize)(this.txtRoleName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDescription.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsActive.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbCopyFromRole.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.TextEdit txtRoleName;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.MemoEdit txtDescription;
        private DevExpress.XtraEditors.CheckEdit chkIsActive;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private System.Windows.Forms.RadioButton radioNoPermissions;
        private System.Windows.Forms.RadioButton radioCopyFromRole;
        private DevExpress.XtraEditors.ComboBoxEdit cmbCopyFromRole;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.SimpleButton btnCancel;
    }
}
